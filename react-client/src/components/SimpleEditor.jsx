import React, { useEffect, useRef, useState } from 'react';
import { Editor } from '@tiptap/core';
import StarterKit from '@tiptap/starter-kit';
import { Typography } from '@tiptap/extension-typography';
import { Dropcursor } from '@tiptap/extension-dropcursor';
import { Gapcursor } from '@tiptap/extension-gapcursor';
import { Underline } from '@tiptap/extension-underline';
import { Link } from '@tiptap/extension-link';
import { TextAlign } from '@tiptap/extension-text-align';
import { Placeholder } from '@tiptap/extension-placeholder';
import { Highlight } from '@tiptap/extension-highlight';
import { Table } from '@tiptap/extension-table';
import { TableCell } from '@tiptap/extension-table-cell';
import { TableHeader } from '@tiptap/extension-table-header';
import { TableRow } from '@tiptap/extension-table-row';
import { TaskList } from '@tiptap/extension-task-list';
import { TaskItem } from '@tiptap/extension-task-item';
import { TextStyle } from '@tiptap/extension-text-style';
import { Color } from '@tiptap/extension-color';
import { Superscript } from '@tiptap/extension-superscript';
import { Subscript } from '@tiptap/extension-subscript';
import { Youtube } from '@tiptap/extension-youtube';
import { Image } from '@tiptap/extension-image';
import Collaboration from '@tiptap/extension-collaboration';
import CollaborationCaret from '@tiptap/extension-collaboration-cursor';
import * as Y from 'yjs';
import { HocuspocusProvider } from '@hocuspocus/provider';

const SimpleEditor = () => {
    const editorRef = useRef(null);
    const [editor, setEditor] = useState(null);
    const [provider, setProvider] = useState(null);
    const [status, setStatus] = useState('disconnected');
    const [error, setError] = useState(null);

    const connect = () => {
        console.log('Starting connection...');
        setError(null);
        setStatus('connecting');

        // Создаем Y.js документ
        const ydoc = new Y.Doc();

        // Создаем провайдер
        const newProvider = new HocuspocusProvider({
            url: 'ws://localhost:3033/collab',
            name: 'test-document',
            document: ydoc,
        });

        // Простые обработчики событий
        newProvider.on('status', ({ status }) => {
            console.log('Provider status:', status);
            setStatus(status);
        });

        newProvider.on('connect', () => {
            console.log('Provider connected!');
            setError(null);
        });

        newProvider.on('disconnect', () => {
            console.log('Provider disconnected');
        });

        // Обработка ошибок
        newProvider.on('close', ({ event }) => {
            console.log('Connection closed:', event);
            if (event.code !== 1000) {
                setError(`Connection closed: ${event.code} - ${event.reason || 'Unknown'}`);
            }
        });

        setProvider(newProvider);

        // Создаем редактор
        const newEditor = new Editor({
            element: editorRef.current,
            extensions: [
                StarterKit.configure({
                    history: false,
                }),
                Typography,
                Dropcursor,
                Gapcursor,
                Underline,
                Link,
                Image,
                TextAlign.configure({ types: ['heading', 'paragraph'] }),
                Table.configure({ resizable: true }),
                TableCell,
                TableHeader,
                TableRow,
                TaskList,
                TaskItem,
                TextStyle,
                Color,
                Highlight.configure({
                    multicolor: true,
                }),
                Placeholder.configure({
                    placeholder: ({ node }) => {
                        if (node.type.name === 'heading') {
                            return 'Заголовок страницы';
                        }
                        return 'Содержимое страницы';
                    },
                }),
                Superscript,
                Subscript,
                Youtube,
                Collaboration.configure({
                    document: ydoc,
                }),
                CollaborationCaret.configure({
                    provider: newProvider,
                    user: {
                        name: 'Test User',
                        color: '#ff0000',
                    },
                }),
            ],
            content: '<p>Hello World! Start typing...</p>',
        });

        setEditor(newEditor);
    };

    const disconnect = () => {
        if (provider) {
            provider.destroy();
            setProvider(null);
        }
        if (editor) {
            editor.destroy();
            setEditor(null);
        }
        setStatus('disconnected');
        setError(null);
    };

    useEffect(() => {
        return () => {
            if (provider) provider.destroy();
            if (editor) editor.destroy();
        };
    }, []);

    return (
        <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
            <h1>Simple TipTap Collaborative Editor Test</h1>

            <div style={{ marginBottom: '20px' }}>
                <p>
                    Status:{' '}
                    <strong
                        style={{
                            color: status === 'connected' ? 'green' : status === 'connecting' ? 'orange' : 'red',
                        }}
                    >
                        {status}
                    </strong>
                </p>

                {error && (
                    <p style={{ color: 'red', backgroundColor: '#ffe6e6', padding: '10px', borderRadius: '4px' }}>
                        Error: {error}
                    </p>
                )}

                <button
                    onClick={status === 'connected' ? disconnect : connect}
                    style={{
                        padding: '10px 20px',
                        backgroundColor: status === 'connected' ? '#dc3545' : '#007bff',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: 'pointer',
                    }}
                >
                    {status === 'connected' ? 'Disconnect' : 'Connect'}
                </button>
            </div>

            <div
                ref={editorRef}
                style={{
                    border: '1px solid #ccc',
                    borderRadius: '4px',
                    minHeight: '200px',
                    padding: '10px',
                    backgroundColor: 'white',
                }}
            />
        </div>
    );
};

export default SimpleEditor;
