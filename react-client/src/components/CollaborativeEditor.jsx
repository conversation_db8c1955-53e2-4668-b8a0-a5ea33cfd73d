import React, { useEffect, useRef, useState } from 'react';
import { Editor } from '@tiptap/core';
import StarterKit from '@tiptap/starter-kit';
import { Typography } from '@tiptap/extension-typography';
import { Dropcursor } from '@tiptap/extension-dropcursor';
import { Gapcursor } from '@tiptap/extension-gapcursor';
import { Underline } from '@tiptap/extension-underline';
import { Link } from '@tiptap/extension-link';
import { TextAlign } from '@tiptap/extension-text-align';
import { Placeholder } from '@tiptap/extension-placeholder';
import { Highlight } from '@tiptap/extension-highlight';
import { Table } from '@tiptap/extension-table';
import { TableCell } from '@tiptap/extension-table-cell';
import { TableHeader } from '@tiptap/extension-table-header';
import { TableRow } from '@tiptap/extension-table-row';
import { TaskList } from '@tiptap/extension-task-list';
import { TaskItem } from '@tiptap/extension-task-item';
import { TextStyle } from '@tiptap/extension-text-style';
import { Color } from '@tiptap/extension-color';
import { Superscript } from '@tiptap/extension-superscript';
import { Subscript } from '@tiptap/extension-subscript';
import { Youtube } from '@tiptap/extension-youtube';
import { Image } from '@tiptap/extension-image';
import Collaboration from '@tiptap/extension-collaboration';
import CollaborationCaret from '@tiptap/extension-collaboration-cursor';
import * as Y from 'yjs';
import { HocuspocusProvider } from '@hocuspocus/provider';

import '../styles/editor.css';

// Встроенные стили
const styles = {
    container: {
        maxWidth: '1024px',
        margin: '0 auto',
        padding: '24px',
        backgroundColor: 'white',
        minHeight: '100vh',
        fontFamily: 'system-ui, -apple-system, sans-serif',
    },
    header: {
        marginBottom: '32px',
    },
    title: {
        fontSize: '30px',
        fontWeight: 'bold',
        color: '#111827',
        marginBottom: '8px',
    },
    subtitle: {
        color: '#6B7280',
    },
    configPanel: {
        backgroundColor: '#F9FAFB',
        borderRadius: '8px',
        padding: '24px',
        marginBottom: '24px',
        border: '1px solid #E5E7EB',
    },
    configTitle: {
        fontSize: '18px',
        fontWeight: '600',
        marginBottom: '16px',
        color: '#374151',
    },
    inputGrid: {
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '16px',
        marginBottom: '16px',
    },
    inputGroup: {
        display: 'flex',
        flexDirection: 'column',
    },
    label: {
        display: 'block',
        fontSize: '14px',
        fontWeight: '500',
        color: '#374151',
        marginBottom: '8px',
    },
    input: {
        width: '100%',
        padding: '8px 12px',
        border: '1px solid #D1D5DB',
        borderRadius: '6px',
        fontSize: '14px',
        outline: 'none',
        transition: 'border-color 0.2s',
    },
    inputFocus: {
        borderColor: '#3B82F6',
        boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.1)',
    },
    inputDisabled: {
        backgroundColor: '#F3F4F6',
        color: '#6B7280',
    },
    statusBar: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    statusIndicator: {
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
    },
    statusDot: {
        width: '12px',
        height: '12px',
        borderRadius: '50%',
    },
    statusConnected: { backgroundColor: '#10B981' },
    statusConnecting: { backgroundColor: '#F59E0B' },
    statusDisconnected: { backgroundColor: '#EF4444' },
    statusText: {
        fontSize: '14px',
        fontWeight: '500',
    },
    button: {
        padding: '8px 16px',
        borderRadius: '6px',
        fontSize: '14px',
        fontWeight: '500',
        border: 'none',
        cursor: 'pointer',
        transition: 'background-color 0.2s',
    },
    buttonConnect: {
        backgroundColor: '#3B82F6',
        color: 'white',
    },
    buttonConnectHover: {
        backgroundColor: '#2563EB',
    },
    buttonDisconnect: {
        backgroundColor: '#EF4444',
        color: 'white',
    },
    buttonDisconnectHover: {
        backgroundColor: '#DC2626',
    },
    editorContainer: {
        backgroundColor: 'white',
        border: '1px solid #E5E7EB',
        borderRadius: '8px',
        boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    },
    editorHeader: {
        borderBottom: '1px solid #E5E7EB',
        backgroundColor: '#F9FAFB',
        padding: '8px 16px',
    },
    editorTitle: {
        fontSize: '14px',
        fontWeight: '500',
        color: '#374151',
    },
    editor: {
        minHeight: '400px',
        maxHeight: '600px',
        overflowY: 'auto',
        padding: '16px',
        outline: 'none',
        border: '1px solid #D1D5DB',
        borderTop: 'none',
        borderRadius: '0 0 8px 8px',
        backgroundColor: 'white',
        fontSize: '16px',
        lineHeight: '1.6',
        fontFamily: 'system-ui, -apple-system, sans-serif',
    },
    infoPanel: {
        marginTop: '24px',
        backgroundColor: '#EFF6FF',
        border: '1px solid #BFDBFE',
        borderRadius: '8px',
        padding: '16px',
    },
    infoTitle: {
        fontSize: '14px',
        fontWeight: '600',
        color: '#1E40AF',
        marginBottom: '8px',
    },
    infoList: {
        fontSize: '14px',
        color: '#1E40AF',
        lineHeight: '1.5',
        paddingLeft: '0',
        listStyle: 'none',
    },
    infoItem: {
        marginBottom: '4px',
    },
};

const CollaborativeEditor = () => {
    const editorRef = useRef(null);
    const [editor, setEditor] = useState(null);
    const [provider, setProvider] = useState(null);
    const [isConnected, setIsConnected] = useState(false);
    const [connectionStatus, setConnectionStatus] = useState('disconnected');
    const [connectionError, setConnectionError] = useState(null);

    // Используем фиксированный идентификатор страницы
    const getFixedPageId = () => {
        return '019759c6-35c8-7799-8f21-2be79530d07d';
    };

    // Настройки подключения
    const [serverUrl, setServerUrl] = useState('ws://localhost:3033/collab');
    const [documentName, setDocumentName] = useState(() => getFixedPageId());
    const [userName, setUserName] = useState('User ' + Math.floor(Math.random() * 1000));
    const [userColor, setUserColor] = useState('#' + Math.floor(Math.random() * 16777215).toString(16));

    const connectToServer = async () => {
        if (provider) {
            provider.destroy();
        }

        setConnectionError(null);
        setConnectionStatus('connecting');

        console.log('Attempting to connect to:', serverUrl);
        console.log('Document name:', documentName);

        // Проверяем доступность сервера (временно отключено из-за CORS)
        // try {
        // 	// Извлекаем хост из serverUrl для HTTP проверки
        // 	const wsUrl = new URL(serverUrl);
        // 	const httpUrl = `http://${wsUrl.host}`;
        // 	console.log('Checking server availability at:', httpUrl);
        //
        // 	const response = await fetch(httpUrl);
        // 	console.log('Server HTTP check:', response.status);
        // } catch (error) {
        // 	console.error('Server not reachable via HTTP:', error);
        // 	setConnectionError(`Сервер недоступен. Убедитесь, что сервер запущен на ${serverUrl}`);
        // 	setConnectionStatus('disconnected');
        // 	return;
        // }

        const ydoc = new Y.Doc();

        // Добавляем обработчик изменений Y.js документа
        ydoc.on('update', (update, origin) => {
            console.log('📝 Y.js document updated');
            console.log('🔢 Update size:', update.length, 'bytes');
            console.log('🔧 Origin:', origin);
            console.log(
                '🔢 Binary data (first 20 bytes):',
                Array.from(update.slice(0, 20))
                    .map((b) => b.toString(16).padStart(2, '0'))
                    .join(' '),
            );

            // Логируем содержимое документа
            try {
                const fragment = ydoc.getXmlFragment('default');
                if (fragment) {
                    console.log('📄 Document content:', fragment.toString());
                }
            } catch (error) {
                console.log('❌ Error reading document content:', error);
            }
        });

        const newProvider = new HocuspocusProvider({
            url: serverUrl,
            name: documentName,
            document: ydoc,
            connect: true,
            preserveConnection: false,
            // Добавляем параметры для отладки
            onOpen: () => {
                console.log('HocuspocusProvider: WebSocket opened');
            },
            onClose: (event) => {
                console.log('HocuspocusProvider: WebSocket closed', event);
            },
            onFail: ({ reason }) => {
                console.error('HocuspocusProvider: Connection failed', reason);
                setConnectionError(`Connection failed: ${reason}`);
            },
        });

        // Обработчики событий для мониторинга соединения
        newProvider.on('status', (event) => {
            console.log('Connection status event:', event);
            const status = event.status || event;
            console.log('Parsed status:', status);
            setConnectionStatus(status);
            setIsConnected(status === 'connected');
            if (status === 'connected') {
                setConnectionError(null);
            }
        });

        newProvider.on('connect', () => {
            console.log('Connected to Hocuspocus server');
            setConnectionStatus('connected');
            setIsConnected(true);
            setConnectionError(null);
        });

        newProvider.on('synced', () => {
            console.log('📄 Document synced - connection is ready');
            console.log('🔢 Document state size:', Y.encodeStateAsUpdate(ydoc).length, 'bytes');
            setConnectionStatus('connected');
            setIsConnected(true);
            setConnectionError(null);
        });

        newProvider.on('disconnect', ({ event }) => {
            console.log('Disconnected from Hocuspocus server', event);
        });

        newProvider.on('close', ({ event }) => {
            console.log('Connection closed', event);
            if (event && event.code !== 1000) {
                setConnectionError(`Connection closed with code ${event.code}: ${event.reason || 'Unknown error'}`);
            }
        });

        newProvider.on('awarenessUpdate', ({ states }) => {
            console.log('Awareness update:', states);
        });

        // Добавляем обработчики WebSocket после небольшой задержки
        setTimeout(() => {
            if (newProvider.ws) {
                newProvider.ws.addEventListener('error', (event) => {
                    console.error('WebSocket error:', event);
                    setConnectionError(`WebSocket error: ${event.message || 'Connection failed'}`);
                });

                newProvider.ws.addEventListener('close', (event) => {
                    console.log('WebSocket closed:', event.code, event.reason);
                    if (event.code !== 1000) {
                        setConnectionError(
                            `WebSocket closed with code ${event.code}: ${event.reason || 'Connection lost'}`,
                        );
                    }
                });
            }
        }, 100);

        setProvider(newProvider);

        // Добавляем таймаут для проверки подключения
        setTimeout(() => {
            if (newProvider.ws && newProvider.ws.readyState === WebSocket.OPEN) {
                console.log('WebSocket is open, setting status to connected');
                setConnectionStatus('connected');
                setIsConnected(true);
                setConnectionError(null);
            }
        }, 1000);

        // Создаем редактор
        const newEditor = new Editor({
            element: editorRef.current,
            extensions: [
                StarterKit.configure({
                    history: false, // Отключаем локальную историю для коллаборации
                }),
                Typography,
                Dropcursor,
                Gapcursor,
                Underline,
                Link,
                Image,
                TextAlign.configure({ types: ['heading', 'paragraph'] }),
                Table.configure({ resizable: true }),
                TableCell,
                TableHeader,
                TableRow,
                TaskList,
                TaskItem,
                TextStyle,
                Color,
                Highlight.configure({
                    multicolor: true,
                }),
                Placeholder.configure({
                    placeholder: ({ node }) => {
                        if (node.type.name === 'heading') {
                            return 'Заголовок страницы';
                        }
                        return 'Содержимое страницы';
                    },
                }),
                Superscript,
                Subscript,
                Youtube,
                Collaboration.configure({
                    document: ydoc,
                }),
                CollaborationCaret.configure({
                    provider: newProvider,
                    user: {
                        name: userName,
                        color: userColor,
                    },
                }),
            ],
            content: '<p>Начните вводить текст для совместного редактирования...</p>',
        });

        setEditor(newEditor);
    };

    const disconnect = () => {
        if (provider) {
            provider.destroy();
            setProvider(null);
        }
        if (editor) {
            editor.destroy();
            setEditor(null);
        }
        setIsConnected(false);
        setConnectionStatus('disconnected');
        setConnectionError(null);
    };

    useEffect(() => {
        // Cleanup при размонтировании компонента
        return () => {
            if (provider) {
                provider.destroy();
            }
            if (editor) {
                editor.destroy();
            }
        };
    }, [provider, editor]);

    const getStatusColor = () => {
        switch (connectionStatus) {
            case 'connected':
                return 'text-green-600';
            case 'connecting':
                return 'text-yellow-600';
            case 'disconnected':
                return 'text-red-600';
            default:
                return 'text-gray-600';
        }
    };

    const getStatusText = () => {
        switch (connectionStatus) {
            case 'connected':
                return 'Подключено';
            case 'connecting':
                return 'Подключение...';
            case 'disconnected':
                return 'Отключено';
            default:
                return 'Неизвестно';
        }
    };

    return (
        <div style={styles.container}>
            <div style={styles.header}>
                <h1 style={styles.title}>Collaborative Editor</h1>
                <p style={styles.subtitle}>Совместный редактор на базе Tiptap и Hocuspocus</p>
            </div>

            {/* Панель подключения */}
            <div style={styles.configPanel}>
                <h2 style={styles.configTitle}>Настройки подключения</h2>

                <div style={styles.inputGrid}>
                    <div style={styles.inputGroup}>
                        <label style={styles.label}>URL сервера</label>
                        <input
                            type="text"
                            value={serverUrl}
                            onChange={(e) => setServerUrl(e.target.value)}
                            placeholder="ws://localhost:3033/collab"
                            style={{
                                ...styles.input,
                                ...(isConnected ? styles.inputDisabled : {}),
                            }}
                            disabled={isConnected}
                        />
                    </div>

                    <div style={styles.inputGroup}>
                        <label style={styles.label}>Имя документа</label>
                        <div style={{ display: 'flex', gap: '8px' }}>
                            <input
                                type="text"
                                value={documentName}
                                onChange={(e) => setDocumentName(e.target.value)}
                                placeholder="019759c6-35c8-7799-8f21-2be79530d07d"
                                style={{
                                    ...styles.input,
                                    flex: 1,
                                    ...(isConnected ? styles.inputDisabled : {}),
                                }}
                                disabled={isConnected}
                            />
                            <button
                                onClick={() => setDocumentName(getFixedPageId())}
                                disabled={isConnected}
                                style={{
                                    padding: '8px 12px',
                                    backgroundColor: isConnected ? '#E5E7EB' : '#6B7280',
                                    color: isConnected ? '#9CA3AF' : 'white',
                                    border: 'none',
                                    borderRadius: '6px',
                                    cursor: isConnected ? 'not-allowed' : 'pointer',
                                    fontSize: '14px',
                                }}
                                title="Сбросить к фиксированному ID"
                            >
                                🎲
                            </button>
                        </div>
                    </div>

                    <div style={styles.inputGroup}>
                        <label style={styles.label}>Имя пользователя</label>
                        <input
                            type="text"
                            value={userName}
                            onChange={(e) => setUserName(e.target.value)}
                            placeholder="Ваше имя"
                            style={{
                                ...styles.input,
                                ...(isConnected ? styles.inputDisabled : {}),
                            }}
                            disabled={isConnected}
                        />
                    </div>

                    <div style={styles.inputGroup}>
                        <label style={styles.label}>Цвет курсора</label>
                        <input
                            type="color"
                            value={userColor}
                            onChange={(e) => setUserColor(e.target.value)}
                            style={{
                                ...styles.input,
                                height: '40px',
                                ...(isConnected ? styles.inputDisabled : {}),
                            }}
                            disabled={isConnected}
                        />
                    </div>
                </div>

                <div style={styles.statusBar}>
                    <div style={styles.statusIndicator}>
                        <div
                            style={{
                                ...styles.statusDot,
                                ...(connectionStatus === 'connected'
                                    ? styles.statusConnected
                                    : connectionStatus === 'connecting'
                                      ? styles.statusConnecting
                                      : styles.statusDisconnected),
                            }}
                        ></div>
                        <span
                            style={{
                                ...styles.statusText,
                                color:
                                    connectionStatus === 'connected'
                                        ? '#059669'
                                        : connectionStatus === 'connecting'
                                          ? '#D97706'
                                          : '#DC2626',
                            }}
                        >
                            {getStatusText()}
                        </span>
                    </div>

                    <div>
                        {!isConnected ? (
                            <button
                                onClick={connectToServer}
                                style={{
                                    ...styles.button,
                                    ...styles.buttonConnect,
                                }}
                                onMouseOver={(e) => (e.target.style.backgroundColor = '#2563EB')}
                                onMouseOut={(e) => (e.target.style.backgroundColor = '#3B82F6')}
                            >
                                Подключиться
                            </button>
                        ) : (
                            <button
                                onClick={disconnect}
                                style={{
                                    ...styles.button,
                                    ...styles.buttonDisconnect,
                                }}
                                onMouseOver={(e) => (e.target.style.backgroundColor = '#DC2626')}
                                onMouseOut={(e) => (e.target.style.backgroundColor = '#EF4444')}
                            >
                                Отключиться
                            </button>
                        )}

                        {/* Кнопка для отладки статуса */}
                        {connectionStatus === 'connecting' && (
                            <button
                                onClick={() => {
                                    console.log('Force setting status to connected');
                                    setConnectionStatus('connected');
                                    setIsConnected(true);
                                }}
                                style={{
                                    ...styles.button,
                                    backgroundColor: '#28a745',
                                    marginLeft: '10px',
                                }}
                            >
                                Принудительно подключить
                            </button>
                        )}
                    </div>
                </div>

                {/* Отображение ошибок подключения */}
                {connectionError && (
                    <div
                        style={{
                            marginTop: '16px',
                            padding: '12px',
                            backgroundColor: '#FEF2F2',
                            border: '1px solid #FECACA',
                            borderRadius: '8px',
                            color: '#DC2626',
                            fontSize: '14px',
                        }}
                    >
                        <strong>Ошибка подключения:</strong> {connectionError}
                    </div>
                )}
            </div>

            {/* Редактор */}
            <div style={styles.editorContainer}>
                <div style={styles.editorHeader}>
                    <h3 style={styles.editorTitle}>Документ: {documentName}</h3>
                </div>
                <div ref={editorRef} style={styles.editor} />
            </div>

            {/* Информация */}
            <div style={styles.infoPanel}>
                <h3 style={styles.infoTitle}>Инструкция по использованию:</h3>
                <ul style={styles.infoList}>
                    <li style={styles.infoItem}>
                        • Укажите URL вашего Hocuspocus сервера (например: ws://localhost:3033/collab)
                    </li>
                    <li style={styles.infoItem}>• Введите имя документа для совместного редактирования</li>
                    <li style={styles.infoItem}>• Настройте имя пользователя и цвет курсора</li>
                    <li style={styles.infoItem}>• Нажмите "Подключиться" для начала совместной работы</li>
                    <li style={styles.infoItem}>
                        • Откройте несколько вкладок с одинаковым именем документа для тестирования коллаборации
                    </li>
                </ul>
            </div>
        </div>
    );
};

export default CollaborativeEditor;
